import React, { Fragment, useState } from 'react'
import { Dialog, Transition, Listbox } from '@headlessui/react'
import { useQuery } from '@tanstack/react-query'
import { CommandTemplate, SSHServer } from '../types/server'
import { listServers, applyTemplateToServer } from '../services/api'
import { BookTemplate, Server, Check, X, AlertTriangle, ChevronDown } from 'lucide-react'

interface ApplyTemplateModalProps {
  isOpen: boolean
  onClose: () => void
  template: CommandTemplate
}

export default function ApplyTemplateModal({ isOpen, onClose, template }: ApplyTemplateModalProps) {
  const [selectedServer, setSelectedServer] = useState<SSHServer | null>(null)
  const [isApplying, setIsApplying] = useState(false)
  const [result, setResult] = useState<{ success: boolean; message: string } | null>(null)

  const { data: servers = [], isLoading } = useQuery({
    queryKey: ['servers'],
    queryFn: listServers,
  })

  async function handleApplyTemplate() {
    if (!selectedServer) return

    try {
      setIsApplying(true)
      setResult(null)

      const response = await applyTemplateToServer(template.id, selectedServer.id)
      
      setResult({
        success: true,
        message: `Template aplicado com sucesso! ${response.commands.length} comandos adicionados ao servidor.`,
      })
    } catch (error) {
      console.error('Erro ao aplicar template:', error)
      setResult({
        success: false,
        message: 'Erro ao aplicar template. Por favor, tente novamente.',
      })
    } finally {
      setIsApplying(false)
    }
  }

  function handleClose() {
    setSelectedServer(null)
    setResult(null)
    onClose()
  }

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-10" onClose={handleClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black bg-opacity-25" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                <Dialog.Title as="div" className="flex justify-between items-center mb-4">
                  <div className="flex items-center gap-2">
                    <BookTemplate className="h-6 w-6 text-primary-500" />
                    <h3 className="text-lg font-medium text-gray-900">
                      Aplicar Template a um Servidor
                    </h3>
                  </div>
                  <button
                    onClick={handleClose}
                    className="text-gray-400 hover:text-gray-500"
                  >
                    <X className="h-5 w-5" />
                  </button>
                </Dialog.Title>

                <div className="mb-4">
                  <p className="text-sm text-gray-500">
                    Selecione um servidor para aplicar o template <strong>{template.name}</strong>.
                    Os comandos do template serão adicionados ao servidor selecionado.
                  </p>
                </div>

                {result ? (
                  <div className={`p-4 mb-4 rounded-md ${result.success ? 'bg-green-50 text-green-800' : 'bg-red-50 text-red-800'}`}>
                    <div className="flex">
                      {result.success ? (
                        <Check className="h-5 w-5 text-green-400 mr-2" />
                      ) : (
                        <AlertTriangle className="h-5 w-5 text-red-400 mr-2" />
                      )}
                      <p className="text-sm">{result.message}</p>
                    </div>
                  </div>
                ) : (
                  <>
                    <div className="mb-4">
                      <label htmlFor="server" className="block text-sm font-medium text-gray-700 mb-1">
                        Servidor
                      </label>
                      {isLoading ? (
                        <div className="animate-pulse h-10 bg-gray-200 rounded"></div>
                      ) : servers.length === 0 ? (
                        <div className="text-sm text-gray-500">
                          Nenhum servidor disponível. Crie um servidor primeiro.
                        </div>
                      ) : (
                        <Listbox value={selectedServer} onChange={setSelectedServer}>
                          <div className="relative">
                            <Listbox.Button className="relative w-full cursor-default rounded-md border border-gray-300 bg-white py-2 pl-10 pr-10 text-left shadow-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500 sm:text-sm">
                              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <Server className="h-5 w-5 text-gray-400" />
                              </div>
                              <span className="block truncate">
                                {selectedServer ? `${selectedServer.name} (${selectedServer.host})` : 'Selecione um servidor'}
                              </span>
                              <span className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                                <ChevronDown className="h-5 w-5 text-gray-400" aria-hidden="true" />
                              </span>
                            </Listbox.Button>

                            <Transition
                              as={Fragment}
                              leave="transition ease-in duration-100"
                              leaveFrom="opacity-100"
                              leaveTo="opacity-0"
                            >
                              <Listbox.Options className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm">
                                {servers.map((server) => (
                                  <Listbox.Option
                                    key={server.id}
                                    className={({ active }) =>
                                      `relative cursor-default select-none py-2 pl-10 pr-4 ${
                                        active ? 'bg-blue-100 text-blue-900' : 'text-gray-900'
                                      }`
                                    }
                                    value={server}
                                  >
                                    {({ selected }) => (
                                      <>
                                        <span className={`block truncate ${selected ? 'font-medium' : 'font-normal'}`}>
                                          {server.name} ({server.host})
                                        </span>
                                        {selected ? (
                                          <span className="absolute inset-y-0 left-0 flex items-center pl-3 text-blue-600">
                                            <Check className="h-5 w-5" aria-hidden="true" />
                                          </span>
                                        ) : null}
                                      </>
                                    )}
                                  </Listbox.Option>
                                ))}
                              </Listbox.Options>
                            </Transition>
                          </div>
                        </Listbox>
                      )}
                    </div>

                    <div className="mb-4 border-t border-gray-200 pt-4">
                      <h4 className="text-sm font-medium text-gray-700 mb-2 flex items-center gap-1">
                        <BookTemplate className="h-4 w-4 text-gray-500" />
                        Comandos no Template:
                      </h4>
                      <div className="max-h-48 overflow-y-auto custom-scrollbar border border-gray-200 rounded-lg p-3 bg-gray-50">
                        <ul className="text-sm text-gray-600 space-y-1 list-disc ml-4">
                          {template.commands.map((cmd) => (
                            <li key={cmd.id}>
                              <span className="font-medium">{cmd.name}</span>
                              {cmd.description && <span className="text-gray-500"> - {cmd.description}</span>}
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  </>
                )}

                <div className="flex justify-end gap-3 pt-4 border-t border-gray-200">
                  <button
                    type="button"
                    onClick={handleClose}
                    className="inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    {result ? 'Fechar' : 'Cancelar'}
                  </button>
                  
                  {!result && (
                    <button
                      type="button"
                      onClick={handleApplyTemplate}
                      disabled={!selectedServer || isApplying}
                      className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                    >
                      {isApplying ? 'Aplicando...' : 'Aplicar Template'}
                    </button>
                  )}
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  )
} 